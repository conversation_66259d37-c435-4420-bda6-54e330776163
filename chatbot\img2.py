import fitz  # PyMuPDF
import openai
import pytesseract
from fpdf import FPDF
from io import BytesIO
from PIL import Image, ImageEnhance
import base64
import mysql.connector
from datetime import datetime
import os
import json
from django.conf import settings
import logging
from .openai_usage_tracker import track_openai_vision_completion

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)


# --- Config ---
openai.api_key = "********************************************************************************************************************************************************************"  # ✅ Use your actual key
pytesseract.pytesseract.tesseract_cmd = r"C:\Users\<USER>\AppData\Local\Programs\Tesseract-OCR\tesseract.exe"

db_config = {
    'host': 'localhost',
    'user': 'root',
    'password': 'phoobesh333',
    'database': 'rough'
}
def get_img2_prompt():
    import os

    BASE_DIR = os.path.dirname(os.path.abspath(__file__))
    PROMPTS_JSON_PATH = r"D:\CURRENT\chatbot\prompt.json"

# Then read the prompt JSON file from PROMPTS_JSON_PATH

    try:
        with open(PROMPTS_JSON_PATH, "r") as f:
            prompts = json.load(f)
        prompt_data = prompts.get("img2")
        if not prompt_data or not prompt_data.get("is_active"):
            return "You are analyzing a technical image. Provide a clear explanation."
        return prompt_data.get("template", "You are analyzing a technical image. Provide a clear explanation.")
    except Exception as e:
        print(f"Error loading prompt from JSON: {e}")
        return "You are analyzing a technical image. Provide a clear explanation."

# --- MySQL fetch ---
def fetch_unsummarized_pdf():
    conn = mysql.connector.connect(**db_config)
    cursor = conn.cursor()
    cursor.execute("SELECT id, file_name, file_data FROM pdf_image_pages WHERE summarized = FALSE LIMIT 1")
    result = cursor.fetchone()
    cursor.close()
    conn.close()
    return result  # (id, file_name, file_data) or None

# --- Update summarized flag ---
def mark_as_summarized(pdf_id):
    conn = mysql.connector.connect(**db_config)
    cursor = conn.cursor()
    cursor.execute("UPDATE pdf_image_pages SET summarized = TRUE WHERE id = %s", (pdf_id,))
    conn.commit()
    cursor.close()
    conn.close()

# --- Save summarized PDF to pdf_files ---
def save_summary_pdf_to_db(filename, pdf_data):
    conn = mysql.connector.connect(**db_config)
    cursor = conn.cursor()
    last_modified = datetime.now()
    cursor.execute("""
        INSERT INTO pdf_files (file_name, file_data, last_modified)
        VALUES (%s, %s, %s)
    """, (filename, pdf_data, last_modified))
    conn.commit()
    cursor.close()
    conn.close()
    print(f"Summary PDF inserted into pdf_files as '{filename}'")

# --- Extract image pages ---
def extract_pages_with_images(pdf_bytes):
    pdf_doc = fitz.open(stream=pdf_bytes, filetype="pdf")
    pages_data = []

    for page_num in range(len(pdf_doc)):
        page = pdf_doc[page_num]
        if page.get_images(full=True):
            pix = page.get_pixmap(dpi=300)
            img_bytes = BytesIO(pix.tobytes("png")).getvalue()
            pages_data.append({
                "page_number": page_num + 1,
                "image_bytes": img_bytes
            })
    pdf_doc.close()
    return pages_data

# --- OCR ---
def ocr_image_bytes(image_bytes):
    try:
        img = Image.open(BytesIO(image_bytes)).convert("L")
        img = ImageEnhance.Contrast(img).enhance(2.0)
        return pytesseract.image_to_string(img, config="--psm 6").strip()
    except Exception as e:
        print(f"OCR error: {e}")
        return ""

# --- GPT Vision ---
def ask_gpt4v_with_image_and_ocr(image_bytes, ocr_text):
    try:
        image_base64 = base64.b64encode(image_bytes).decode('utf-8')
        image_url = f"data:image/png;base64,{image_base64}"

        ocr_instruction = f"OCR Text:\n{ocr_text}" if ocr_text else "No OCR text available. Focus on visual analysis."

        base_prompt = get_img2_prompt()
        prompt = f"{base_prompt}\n\n{ocr_instruction}"


        response = track_openai_vision_completion(
            model="gpt-4o",
            messages=[
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {"type": "image_url", "image_url": {"url": image_url}}
                    ]
                }
            ],
            max_tokens=700,
            purpose="image_analysis"
        )

        return response.choices[0].message.content.strip()
    except Exception as e:
        print("GPT-4o Vision error:", e)
        return None

# --- Save PDF ---
def save_summaries_to_pdf(summaries):
    pdf = FPDF()
    pdf.set_auto_page_break(auto=True, margin=15)
    pdf.set_font("Arial", size=11)

    def clean(text):
        return text.encode("latin-1", "replace").decode("latin-1")

    for s in summaries:
        pdf.add_page()
        title = f"Page {s['page']} Summary:\n\n"
        pdf.multi_cell(0, 10, clean(title) + clean(s["summary"]))

    return pdf.output(dest='S').encode('latin-1')

# --- Full flow ---
def summarize_from_db():
    while True:
        pdf_entry = fetch_unsummarized_pdf()
        if not pdf_entry:
            print("✅ All image PDFs have been summarized.")
            break

        pdf_id, original_name, pdf_bytes = pdf_entry
        print(f"📄 Processing: {original_name} (id: {pdf_id})")

        pages = extract_pages_with_images(pdf_bytes)
        summaries = []

        for page in pages:
            print(f"🔍 Page {page['page_number']} OCR + GPT summarizing...")
            ocr = ocr_image_bytes(page["image_bytes"])
            summary = ask_gpt4v_with_image_and_ocr(page["image_bytes"], ocr)
            if summary:
                summaries.append({"page": page["page_number"], "summary": summary})

        if not summaries:
            print("⚠️ No summaries generated.")
            mark_as_summarized(pdf_id)
            continue

        summarized_pdf_bytes = save_summaries_to_pdf(summaries)
        summary_filename = f"{original_name.rsplit('.', 1)[0]}_summary.pdf"

        save_summary_pdf_to_db(summary_filename, summarized_pdf_bytes)
        mark_as_summarized(pdf_id)
        print(f"✅ Completed summarization for: {original_name}\n")


# --- Run ---
if __name__ == "__main__":
    summarize_from_db()
